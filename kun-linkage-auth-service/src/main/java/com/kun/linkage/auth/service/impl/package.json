{"authFlow": {"acquireReferenceNo": "************", "approveCode": "LJIV1L", "cardAcceptorCity": "HongKong               ", "cardAcceptorCountryCode": "HK", "cardAcceptorId": "***************", "cardAcceptorName": "ACQUIRER NAME", "cardAcceptorTid": "RECTRS01", "cardProductCode": "VC03", "cardholderBillingAmount": 10.01, "cardholderBillingCurrency": "USD", "cardholderCurrencyExponent": 2, "cardholderMarkupBillingAmount": 10.16, "clearFlag": "N", "conversionRateCardholderBilling": 0.1, "createTime": *************, "customerId": "1947198960607137793", "gatewayCardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "id": "2032794449104404480", "issuerCardId": "************", "markupAmount": 0.15, "markupRate": 0.0146, "maskedCardNo": "441359******7535", "mcc": "5411", "merchantName": "uu international", "merchantNo": "********", "mti": "1100", "originalProcessorTransId": "", "posEntryMode": "000150V05000", "processingCode": "000000", "processor": "BPC-GW", "processorCardId": "870357958VC00000021", "processorExt1": "************", "processorRequestId": "S********000000051", "processorTransId": "S********000000051", "releaseFlag": "L", "remainingBillingAmount": 10.01, "remainingMarkupBillingAmount": 10.16, "remainingTransAmount": 100.01, "status": "PENDING", "systemsTraceAuditNumber": "804051", "thirdPartyAuthorizationFlag": 0, "transAccountingDate": "********", "transAmount": 100.01, "transCurrency": "HKD", "transCurrencyExponent": 2, "transFee": 0, "transType": "010000", "transactionLocalDatetime": "************"}, "authFlowExt": {"authFlowId": "2032794449104404480", "cardholderAccountFlag": 1, "createTime": *************, "kunMid": "********", "merchantAccountFlag": 0, "mpcGroupCode": "KVCC", "mpcTenantId": "1009", "thirdPartyAuthorizationFlag": 0}, "baseAuthRequestVO": {"acquireReferenceNo": "************", "cardAcceptorId": "***************", "cardAcceptorTid": "RECTRS01", "cardholderAmount": 10.01, "cardholderCurrency": "USD", "cardholderCurrencyExponent": 2, "cardholderMarkupAmount": 10.16, "conversionRateCardholderBilling": 0.1, "data": {"requestId": "S********000000051", "transType": 1, "authorizationType": 11, "accountingDirection": 1, "referenceNo": "************", "transId": "S********000000051", "originalTransId": "", "cardId": "870357958VC00000021", "channelData": {"accountIdentification": "T*********355", "acquiringInstitutionIdentificationCode": "476119", "additionalAmounts": {"acquirerFeeAmount": "D************", "issuerFeeAmountAndCurrency": {"feeAmoun": "************", "feeCurrency": "840", "feeSign": "C"}, "transactionAmountAccountCurrency": "***********"}, "additionalData": {"acquierInstitueIdentifier": "9002", "acquirerCountryCode": "344", "acquirerNetworkIdentifier": "0999", "cardId": "************", "cardType": "00", "customerId": "*********", "customerMobilePhone": "***********", "feTraceNumber": "************", "feTransactionDateAndTime": "********081156", "feTransactionNumber": "************", "issuerNetworkIdentifier": "0002", "localTransactionDateAndTime": "************", "networkReferenceData": "***************", "posEnvironmen": "R", "senderReferenceNumber": "************", "settlementType": "2", "svfeTransactionType": "680", "transactionRiskScore": "09"}, "amountAccount": "************", "amountCardholderBilling": "************", "amountTransaction": "************", "cardAcceptorIdentificationCode": "***************", "cardAcceptorNameAndLocation": {"countryCode": "HK", "merchantNumberOfBankName": "ACQUIRER NAME>HongKong               "}, "cardAcceptorTerminalIdentification": "RECTRS01", "conversionRateAccount": "********", "conversionRateCardholderBilling": "********", "currencyCodeAccount": "840", "currencyCodeCardholderBilling": "840", "currencyCodeTransaction": "344", "dateExpiration": "2806", "dateTimeLocalTransaction": "************", "merchantType": "5411", "messageType": "1100", "pointOfServiceDateCode": {"cardCaptureCapabilit": "0", "cardDataInputCapability": "0", "cardDataInputMode": "V", "cardDataOutputCapability": "0", "cardPresence": "0", "cardholderAuthenticationCapability": "0", "cardholderAuthenticationEntity": "5", "cardholderAuthenticationMethod": "0", "cardholderPresenceIndicator": "5", "operatingEnvironmen": "1", "pinCaptureCapability": "0", "terminalOutputCapability": "0"}, "processingCode": "000000", "retrievalReferenceNumber": "************", "settlementDate": "250804", "svfeIssuerInstitutionIdentifier": "7091", "systemsTraceAuditNumber": "804051", "transmissionDateAndTime": "**********"}}, "errorCode": "0000", "errorMessage": "Success", "gatewayCardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "issuerCardId": "************", "mID": "********", "markupAmount": 0.15, "markupRate": 0.0146, "mcc": "5411", "originalTransId": "", "processor": "BPC-GW", "processorCardId": "870357958VC00000021", "processorExt1": "************", "requestId": "S********000000051", "status": "SUCC", "systemsTraceAuditNumber": "804051", "transAmount": 100.01, "transCurrency": "HKD", "transCurrencyExponent": 2, "transId": "S********000000051", "transType": "010000"}, "baseAuthResponseVO": {"requestId": "S********000000051", "returnCode": "0000"}, "customerBasicAccount": {"accountNo": "0011947560348383203330", "accountType": "001", "createTime": "2025-07-22T15:32:25", "currencyCode": "USD", "customerId": "1947198960607137793", "id": 1947560348879151105, "lastModifyTime": "2025-07-22T15:32:25", "organizationNo": "********", "status": "VALID"}, "logContext": {"spanId": "1", "traceId": "1328ed85f99341799a75e9c0cb177888"}, "merchantAccountingDetailReversalFlag": true, "organizationBasicInfo": {"businessType": "01", "checkCustomerAccountFlag": 1, "checkOrganizationAccountFlag": 0, "countryCode": "HK", "createTime": "2025-06-26T16:31:44", "createUserId": "********", "createUserName": "uu international", "id": 9, "isKycReported": 0, "isKycVerified": 0, "key": "1938153188920758272", "kunMid": "********", "lastModifyTime": "2025-08-01T17:03:52", "lastModifyUserId": "-999999", "lastModifyUserName": "System", "mode": "2", "mpcGroupCode": "KVCC", "mpcTenantId": "1009", "mpcToken": "KVCC", "organizationName": "uu international", "organizationNo": "********", "poolCurrencyCode": "USDT", "sensitiveKey": "rw85EKdoILrNaoc/pu0QLTVpsN2uWG+AkNHBqrzFCLU=", "status": "VALID", "thirdPartyAuthorizationFlag": 0}, "organizationCustomerCardInfo": {"cardActiveStatus": "ACTIVATED", "cardId": "K-0ee843a18b4e4717b16d2c60dbd94b56", "cardNo": "35a02765fe5c7ba278496d60c698882d826049166d7c913e87d44a008b8ff03c", "cardProductCode": "VC03", "cardScheme": "0", "cardStatus": "NORMAL", "createTime": "2025-07-22T15:32:25", "currencyCode": "USD", "customerId": "1947198960607137793", "email": "<EMAIL>", "id": 1947560346203185153, "lastModifyTime": "2025-07-23T15:44:13", "maskedCardNo": "441359******7535", "organizationNo": "********", "processor": "BPC-GW"}, "organizationFeeTemplateDetail": {"billingDimension": "TIERED_SINGLE_AMOUNT", "collectionMethod": "MONTHLY_SETTLEMENT", "createTime": "2025-08-04T15:35:20", "createUserId": "-999999", "createUserName": "System", "currencyCode": "USD", "feeType": "04", "fixedAmount": 1.0, "lastModifyTime": "2025-08-04T15:35:20", "lastModifyUserId": "-999999", "lastModifyUserName": "System", "maxAmount": 50.0, "minAmount": 0.0, "proportionMinAmount": 0.3, "proportionRate": 0.01, "templateDetailId": "1952272123061706753", "templateNo": "1950090805930295298"}, "retryTimes": 5, "technicalParams": {"createTime": "2025-08-01T19:03:25", "id": 6, "lastModifyTime": "2025-08-01T19:03:25", "organizationNo": "********", "status": "VALID", "webhookEnabled": 0}, "thirdPartyAuthorizationFlag": false, "transactionId": "2032794449104404480", "transactionTypeCatogoryEnum": "SALES", "transactionTypeEnum": "AUTHORIZATION"}